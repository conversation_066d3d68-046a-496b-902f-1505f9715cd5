import { useState, useEffect, useRef } from 'react';
import { Platform, Alert } from 'react-native';
import * as Location from 'expo-location';
import { UserLocation } from '@/types/geo.types';
import { saveLastLocationToStorage } from '@/services/storageService';

const LOCATION_TASK_NAME = 'golf-gps-location';

export const useLocationTracking = () => {
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const locationSubscription = useRef<Location.LocationSubscription | null>(null);

  const handleLocationUpdate = (location: Location.LocationObject) => {
    const accuracy = location.coords.accuracy || 100;
    const newLocation: UserLocation = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      accuracy: accuracy,
    };

    console.log('Location update received:', {
      lat: newLocation.latitude,
      lng: newLocation.longitude,
      accuracy: newLocation.accuracy
    });

    // Update if no location or the new one is more accurate
    if (!userLocation || accuracy < userLocation.accuracy) {
      console.log('Updating user location with better accuracy');
      setUserLocation(newLocation);
      saveLastLocationToStorage(newLocation);
    }
  };

  const startLocationTracking = async () => {
    if (!hasPermission) {
      console.log('No location permission, skipping tracking start');
      return;
    }

    console.log('Starting location tracking...');
    try {
      // Enable network provider and check if location services are enabled
      await Location.enableNetworkProviderAsync();
      const servicesEnabled = await Location.hasServicesEnabledAsync();
      console.log('Location services enabled:', servicesEnabled);

      // Get a quick initial location to speed up map centering
      const quickLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced
      });
      if (quickLocation) {
        handleLocationUpdate(quickLocation);
      }

      // iOS-specific background updates to maintain better GPS accuracy
      if (Platform.OS === 'ios') {
        const { status } = await Location.requestBackgroundPermissionsAsync();
        if (status === 'granted') {
          await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
            accuracy: Location.Accuracy.BestForNavigation,
            timeInterval: 500,
            distanceInterval: 0.1,
            activityType: Location.ActivityType.Fitness,
            showsBackgroundLocationIndicator: true,
          });
        }
      }

      // Start immediate high-accuracy updates
      locationSubscription.current = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: 500,
          distanceInterval: 0.1,
          mayShowUserSettingsDialog: true,
        },
        handleLocationUpdate
      );

    } catch (error) {
      console.error('Location tracking error:', error);
      Alert.alert(
        'Error',
        'Failed to start location tracking. Please check your device settings.'
      );
    }
  };

  // Request foreground (during active use) and background (during lock screen, etc) location permissions
  useEffect(() => {
    const requestPermissions = async () => {
      try {
        console.log('Requesting location permissions...');
        const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
        console.log('Foreground permission status:', foregroundStatus);

        if (foregroundStatus !== 'granted') {
          Alert.alert(
            'Permission Denied',
            'Location permission is required to track your golf shots.'
          );
          return;
        }

        const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
        console.log('Background permission status:', backgroundStatus);

        if (backgroundStatus !== 'granted') {
          Alert.alert(
            'Background Permission Denied',
            'Background location permission is recommended for GPS responsiveness.'
          );
        }

        // Set permission to true if we have at least foreground permission
        console.log('Setting hasPermission to true');
        setHasPermission(foregroundStatus === 'granted');
      } catch (error) {
        console.error('Permission request error:', error);
        Alert.alert(
          'Error',
          'Failed to request location permissions. Please check your device settings.'
        );
      }
    };

    requestPermissions();
  }, []);

  // Start/stop tracking based on permission
  useEffect(() => {
    if (hasPermission) {
      startLocationTracking();
    }

    // Cleanup function to remove listeners
    return () => {
      locationSubscription.current?.remove();
      if (Platform.OS === 'ios') {
        Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME).catch(console.error);
      }
    };
  }, [hasPermission, userLocation]); // Add userLocation to deps to avoid stale closure

  return { userLocation, hasPermission };
};
