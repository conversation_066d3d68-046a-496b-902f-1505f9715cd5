import { useState, useEffect, useRef } from 'react';
import { Platform, Alert } from 'react-native';
import * as Location from 'expo-location';
import { UserLocation } from '@/types/geo.types';
import { saveLastLocationToStorage } from '@/services/storageService';

const LOCATION_TASK_NAME = 'golf-gps-location';

export const useLocationTracking = () => {
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const locationSubscription = useRef<Location.LocationSubscription | null>(null);

  const handleLocationUpdate = (location: Location.LocationObject) => {
    const accuracy = location.coords.accuracy || 100;
    const newLocation: UserLocation = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      accuracy: accuracy,
    };

    // Update location state and save to storage
    setUserLocation(prevLocation => {
      // Only update if no previous location or the new one is more accurate
      if (!prevLocation || accuracy < prevLocation.accuracy) {
        saveLastLocationToStorage(newLocation);
        return newLocation;
      }
      return prevLocation;
    });
  };

  const startLocationTracking = async () => {
    try {
      // Enable network provider and check if location services are enabled
      await Location.enableNetworkProviderAsync();
      await Location.hasServicesEnabledAsync();

      // Get a quick initial location to speed up map centering
      const quickLocation = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced
      });
      if (quickLocation) {
        handleLocationUpdate(quickLocation);
      }

      // iOS-specific background updates to maintain better GPS accuracy
      if (Platform.OS === 'ios') {
        await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: 500,
          distanceInterval: 0.1,
          activityType: Location.ActivityType.Fitness,
          showsBackgroundLocationIndicator: true,
        });
      }

      // Start immediate high-accuracy updates
      locationSubscription.current = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: 500,
          distanceInterval: 0.1,
          mayShowUserSettingsDialog: true,
        },
        handleLocationUpdate
      );

    } catch (error) {
      console.error('Location tracking error:', error);
      Alert.alert(
        'Error',
        'Failed to start location tracking. Please check your device settings.'
      );
    }
  };

  // Initialize location tracking with permissions
  useEffect(() => {
    const initializeLocationTracking = async () => {
      try {
        // Request permissions first
        const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();

        if (foregroundStatus !== 'granted') {
          Alert.alert(
            'Permission Denied',
            'Location permission is required to track your golf shots.'
          );
          return;
        }

        const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();

        if (backgroundStatus !== 'granted') {
          Alert.alert(
            'Background Permission Denied',
            'Background location permission is recommended for GPS responsiveness.'
          );
        }

        // Set permission state
        setHasPermission(true);

        // Start location tracking immediately after getting permission
        await startLocationTracking();

      } catch (error) {
        console.error('Location initialization error:', error);
        Alert.alert(
          'Error',
          'Failed to initialize location tracking. Please check your device settings.'
        );
      }
    };

    initializeLocationTracking();

    // Cleanup function
    return () => {
      locationSubscription.current?.remove();
      if (Platform.OS === 'ios') {
        Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME).catch(console.error);
      }
    };
  }, []); // Empty dependency array - only run once on mount

  return { userLocation, hasPermission };
};
