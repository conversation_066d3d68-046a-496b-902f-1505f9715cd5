import { useState, useEffect, useRef } from 'react';
import { Platform, AppState } from 'react-native';
import * as Location from 'expo-location';
import { UserLocation } from '@/types/geo.types';
import { saveLastLocationToStorage } from '@/services/storageService';

const LOCATION_TASK_NAME = 'golf-gps-location';

export const useLocationTracking = () => {
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const locationSubscription = useRef<Location.LocationSubscription | null>(null);

  const handleLocationUpdate = (location: Location.LocationObject) => {
    const newLocation: UserLocation = {
      latitude: location.coords.latitude,
      longitude: location.coords.longitude,
      accuracy: location.coords.accuracy || 100,  // Default to 100m accuracy
    };

    // Update if no location or the new one is more accurate
    if (!userLocation || newLocation.accuracy < userLocation.accuracy) {
      setUserLocation(newLocation);
      saveLastLocationToStorage(newLocation);
    }
  };

  const startLocationTracking = async () => {
    // if (!hasPermission) return;

    try {
      // Get a quick initial location to speed up map centering
      const quickLocation = await Location.getCurrentPositionAsync({ accuracy: Location.Accuracy.Balanced });
      if (quickLocation) {
        handleLocationUpdate(quickLocation);
      }

      // Start high-accuracy updates for when the app is active
      locationSubscription.current = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.BestForNavigation,
          timeInterval: 1000,
          distanceInterval: 1,
        },
        handleLocationUpdate
      );
      
      // Start background task for iOS for better responsiveness
      if (Platform.OS === 'ios') {
          await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
            accuracy: Location.Accuracy.BestForNavigation,
            activityType: Location.ActivityType.Fitness,
            showsBackgroundLocationIndicator: true,
            pausesUpdatesAutomatically: false,
          });
      }

    } catch (error) {
      console.error('Location tracking error:', error);
    }
  };

  // Request foreground (during active use) and background (during lock screen, etc) location permissions
  useEffect(() => {
    const requestPermissions = async () => {
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      if (foregroundStatus !== 'granted') {
        // TODO: Handle permission denial. User has to edit phone settings.
        return;
      }
      
      const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
      if (backgroundStatus !== 'granted') {
        // TODO: Handle permission denial. User has to edit phone settings.
        return;
      }

      if (foregroundStatus === 'granted' && backgroundStatus === 'granted') {
        setHasPermission(true);
      }
    };

    requestPermissions();
  }, []);

  // Start/stop tracking based on permission
  useEffect(() => {
    if (hasPermission) {
      startLocationTracking();
    }

    // Cleanup function to remove listeners
    return () => {
      locationSubscription.current?.remove();
      if (Platform.OS === 'ios') {
        Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
      }
    };
  }, [hasPermission]);

  return { userLocation, hasPermission };
};
